/**
 * @license
 * Copyright 2010-2025 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
import{TSL as e}from"three/webgpu";const t=e.BRDF_GGX,r=e.BRDF_Lambert,a=e.BasicShadowFilter,o=e.Break,i=e.Continue,n=e.DFGApprox,s=e.D_GGX,l=e.Discard,c=e.EPSILON,m=e.F_Schlick,p=e.Fn,d=e.INFINITY,u=e.If,g=e.Loop,h=e.NodeShaderStage,x=e.NodeType,f=e.NodeUpdateType,b=e.NodeAccess,w=e.PCFShadowFilter,v=e.PCFSoftShadowFilter,S=e.PI,T=e.PI2,_=e.Return,V=e.Schlick_to_F0,y=e.ScriptableNodeResources,D=e.ShaderNode,M=e.TBNViewMatrix,F=e.VSMShadowFilter,C=e.V_GGX_SmithCorrelated,I=e.abs,P=e.acesFilmicToneMapping,N=e.acos,R=e.add,B=e.addNodeElement,L=e.agxToneMapping,k=e.all,A=e.alphaT,G=e.and,O=e.anisotropy,W=e.anisotropyB,j=e.anisotropyT,U=e.any,z=e.append,q=e.array,E=e.arrayBuffer,Z=e.asin,X=e.assign,Y=e.atan,H=e.atan2,J=e.atomicAdd,K=e.atomicAnd,Q=e.atomicFunc,$=e.atomicMax,ee=e.atomicMin,te=e.atomicOr,re=e.atomicStore,ae=e.atomicSub,oe=e.atomicXor,ie=e.atomicLoad,ne=e.attenuationColor,se=e.attenuationDistance,le=e.attribute,ce=e.attributeArray,me=e.backgroundBlurriness,pe=e.backgroundIntensity,de=e.backgroundRotation,ue=e.batch,ge=e.billboarding,he=e.bitAnd,xe=e.bitNot,fe=e.bitOr,be=e.bitXor,we=e.bitangentGeometry,ve=e.bitangentLocal,Se=e.bitangentView,Te=e.bitangentWorld,_e=e.bitcast,Ve=e.blendBurn,ye=e.blendColor,De=e.blendDodge,Me=e.blendOverlay,Fe=e.blendScreen,Ce=e.blur,Ie=e.bool,Pe=e.buffer,Ne=e.bufferAttribute,Re=e.bumpMap,Be=e.burn,Le=e.bvec2,ke=e.bvec3,Ae=e.bvec4,Ge=e.bypass,Oe=e.cache,We=e.call,je=e.cameraFar,Ue=e.cameraIndex,ze=e.cameraNear,qe=e.cameraNormalMatrix,Ee=e.cameraPosition,Ze=e.cameraProjectionMatrix,Xe=e.cameraProjectionMatrixInverse,Ye=e.cameraViewMatrix,He=e.cameraWorldMatrix,Je=e.cbrt,Ke=e.cdl,Qe=e.ceil,$e=e.checker,et=e.cineonToneMapping,tt=e.clamp,rt=e.clearcoat,at=e.clearcoatRoughness,ot=e.code,it=e.color,nt=e.colorSpaceToWorking,st=e.colorToDirection,lt=e.compute,ct=e.computeSkinning,mt=e.cond,pt=e.Const,dt=e.context,ut=e.convert,gt=e.convertColorSpace,ht=e.convertToTexture,xt=e.cos,ft=e.cross,bt=e.cubeTexture,wt=e.dFdx,vt=e.dFdy,St=e.dashSize,Tt=e.debug,_t=e.defaultBuildStages,Vt=e.defaultShaderStages,yt=e.defined,Dt=e.degrees,Mt=e.deltaTime,Ft=e.densityFog,Ct=e.densityFogFactor,It=e.depth,Pt=e.depthPass,Nt=e.difference,Rt=e.diffuseColor,Bt=e.directPointLight,Lt=e.directionToColor,kt=e.dispersion,At=e.distance,Gt=e.div,Ot=e.dodge,Wt=e.dot,jt=e.drawIndex,Ut=e.dynamicBufferAttribute,zt=e.element,qt=e.emissive,Et=e.equal,Zt=e.equals,Xt=e.equirectUV,Yt=e.exp,Ht=e.exp2,Jt=e.expression,Kt=e.faceDirection,Qt=e.faceForward,$t=e.faceforward,er=e.float,tr=e.floor,rr=e.fog,ar=e.fract,or=e.frameGroup,ir=e.frameId,nr=e.frontFacing,sr=e.fwidth,lr=e.gain,cr=e.gapSize,mr=e.getConstNodeType,pr=e.getCurrentStack,dr=e.getDirection,ur=e.getDistanceAttenuation,gr=e.getGeometryRoughness,hr=e.getNormalFromDepth,xr=e.getParallaxCorrectNormal,fr=e.getRoughness,br=e.getScreenPosition,wr=e.getShIrradianceAt,vr=e.getTextureIndex,Sr=e.getViewPosition,Tr=e.glsl,_r=e.glslFn,Vr=e.grayscale,yr=e.greaterThan,Dr=e.greaterThanEqual,Mr=e.hash,Fr=e.highpModelNormalViewMatrix,Cr=e.highpModelViewMatrix,Ir=e.hue,Pr=e.instance,Nr=e.instanceIndex,Rr=e.instancedArray,Br=e.instancedBufferAttribute,Lr=e.instancedDynamicBufferAttribute,kr=e.instancedMesh,Ar=e.int,Gr=e.inverseSqrt,Or=e.inversesqrt,Wr=e.invocationLocalIndex,jr=e.invocationSubgroupIndex,Ur=e.ior,zr=e.iridescence,qr=e.iridescenceIOR,Er=e.iridescenceThickness,Zr=e.ivec2,Xr=e.ivec3,Yr=e.ivec4,Hr=e.js,Jr=e.label,Kr=e.length,Qr=e.lengthSq,$r=e.lessThan,ea=e.lessThanEqual,ta=e.lightPosition,ra=e.lightTargetDirection,aa=e.lightTargetPosition,oa=e.lightViewPosition,ia=e.lightingContext,na=e.lights,sa=e.linearDepth,la=e.linearToneMapping,ca=e.localId,ma=e.globalId,pa=e.log,da=e.log2,ua=e.logarithmicDepthToViewZ,ga=e.loop,ha=e.luminance,xa=e.mediumpModelViewMatrix,fa=e.mat2,ba=e.mat3,wa=e.mat4,va=e.matcapUV,Sa=e.materialAO,Ta=e.materialAlphaTest,_a=e.materialAnisotropy,Va=e.materialAnisotropyVector,ya=e.materialAttenuationColor,Da=e.materialAttenuationDistance,Ma=e.materialClearcoat,Fa=e.materialClearcoatNormal,Ca=e.materialClearcoatRoughness,Ia=e.materialColor,Pa=e.materialDispersion,Na=e.materialEmissive,Ra=e.materialIOR,Ba=e.materialIridescence,La=e.materialIridescenceIOR,ka=e.materialIridescenceThickness,Aa=e.materialLightMap,Ga=e.materialLineDashOffset,Oa=e.materialLineDashSize,Wa=e.materialLineGapSize,ja=e.materialLineScale,Ua=e.materialLineWidth,za=e.materialMetalness,qa=e.materialNormal,Ea=e.materialOpacity,Za=e.materialPointSize,Xa=e.materialReference,Ya=e.materialReflectivity,Ha=e.materialRefractionRatio,Ja=e.materialRotation,Ka=e.materialRoughness,Qa=e.materialSheen,$a=e.materialSheenRoughness,eo=e.materialShininess,to=e.materialSpecular,ro=e.materialSpecularColor,ao=e.materialSpecularIntensity,oo=e.materialSpecularStrength,io=e.materialThickness,no=e.materialTransmission,so=e.max,lo=e.maxMipLevel,co=e.metalness,mo=e.min,po=e.mix,uo=e.mixElement,go=e.mod,ho=e.modInt,xo=e.modelDirection,fo=e.modelNormalMatrix,bo=e.modelPosition,wo=e.modelRadius,vo=e.modelScale,So=e.modelViewMatrix,To=e.modelViewPosition,_o=e.modelViewProjection,Vo=e.modelWorldMatrix,yo=e.modelWorldMatrixInverse,Do=e.morphReference,Mo=e.mrt,Fo=e.mul,Co=e.mx_aastep,Io=e.mx_cell_noise_float,Po=e.mx_contrast,No=e.mx_fractal_noise_float,Ro=e.mx_fractal_noise_vec2,Bo=e.mx_fractal_noise_vec3,Lo=e.mx_fractal_noise_vec4,ko=e.mx_hsvtorgb,Ao=e.mx_noise_float,Go=e.mx_noise_vec3,Oo=e.mx_noise_vec4,Wo=e.mx_ramplr,jo=e.mx_ramptb,Uo=e.mx_rgbtohsv,zo=e.mx_safepower,qo=e.mx_splitlr,Eo=e.mx_splittb,Zo=e.mx_srgb_texture_to_lin_rec709,Xo=e.mx_transform_uv,Yo=e.mx_worley_noise_float,Ho=e.mx_worley_noise_vec2,Jo=e.mx_worley_noise_vec3,Ko=e.negate,Qo=e.neutralToneMapping,$o=e.nodeArray,ei=e.nodeImmutable,ti=e.nodeObject,ri=e.nodeObjects,ai=e.nodeProxy,oi=e.normalFlat,ii=e.normalGeometry,ni=e.normalLocal,si=e.normalMap,li=e.normalView,ci=e.normalWorld,mi=e.normalize,pi=e.not,di=e.notEqual,ui=e.numWorkgroups,gi=e.objectDirection,hi=e.objectGroup,xi=e.objectPosition,fi=e.objectRadius,bi=e.objectScale,wi=e.objectViewPosition,vi=e.objectWorldMatrix,Si=e.oneMinus,Ti=e.or,_i=e.orthographicDepthToViewZ,Vi=e.oscSawtooth,yi=e.oscSine,Di=e.oscSquare,Mi=e.oscTriangle,Fi=e.output,Ci=e.outputStruct,Ii=e.overlay,Pi=e.overloadingFn,Ni=e.parabola,Ri=e.parallaxDirection,Bi=e.parallaxUV,Li=e.parameter,ki=e.pass,Ai=e.passTexture,Gi=e.pcurve,Oi=e.perspectiveDepthToViewZ,Wi=e.pmremTexture,ji=e.pointUV,Ui=e.pointWidth,zi=e.positionGeometry,qi=e.positionLocal,Ei=e.positionPrevious,Zi=e.positionView,Xi=e.positionViewDirection,Yi=e.positionWorld,Hi=e.positionWorldDirection,Ji=e.posterize,Ki=e.pow,Qi=e.pow2,$i=e.pow3,en=e.pow4,tn=e.property,rn=e.radians,an=e.rand,on=e.range,nn=e.rangeFog,sn=e.rangeFogFactor,ln=e.reciprocal,cn=e.lightProjectionUV,mn=e.reference,pn=e.referenceBuffer,dn=e.reflect,un=e.reflectVector,gn=e.reflectView,hn=e.reflector,xn=e.refract,fn=e.refractVector,bn=e.refractView,wn=e.reinhardToneMapping,vn=e.remainder,Sn=e.remap,Tn=e.remapClamp,_n=e.renderGroup,Vn=e.renderOutput,yn=e.rendererReference,Dn=e.rotate,Mn=e.rotateUV,Fn=e.roughness,Cn=e.round,In=e.rtt,Pn=e.sRGBTransferEOTF,Nn=e.sRGBTransferOETF,Rn=e.sampler,Bn=e.samplerComparison,Ln=e.saturate,kn=e.saturation,An=e.screen,Gn=e.screenCoordinate,On=e.screenSize,Wn=e.screenUV,jn=e.scriptable,Un=e.scriptableValue,zn=e.select,qn=e.setCurrentStack,En=e.shaderStages,Zn=e.shadow,Xn=e.pointShadow,Yn=e.shadowPositionWorld,Hn=e.sharedUniformGroup,Jn=e.shapeCircle,Kn=e.sheen,Qn=e.sheenRoughness,$n=e.shiftLeft,es=e.shiftRight,ts=e.shininess,rs=e.sign,as=e.sin,os=e.sinc,is=e.skinning,ns=e.smoothstep,ss=e.smoothstepElement,ls=e.specularColor,cs=e.specularF90,ms=e.spherizeUV,ps=e.split,ds=e.spritesheetUV,us=e.sqrt,gs=e.stack,hs=e.step,xs=e.storage,fs=e.storageBarrier,bs=e.storageObject,ws=e.storageTexture,vs=e.string,Ss=e.struct,Ts=e.sub,_s=e.subgroupIndex,Vs=e.subgroupSize,ys=e.tan,Ds=e.tangentGeometry,Ms=e.tangentLocal,Fs=e.tangentView,Cs=e.tangentWorld,Is=e.temp,Ps=e.texture,Ns=e.texture3D,Rs=e.textureBarrier,Bs=e.textureBicubic,Ls=e.textureCubeUV,ks=e.textureLoad,As=e.textureSize,Gs=e.textureStore,Os=e.thickness,Ws=e.threshold,js=e.time,Us=e.timerDelta,zs=e.timerGlobal,qs=e.timerLocal,Es=e.toOutputColorSpace,Zs=e.toWorkingColorSpace,Xs=e.toneMapping,Ys=e.toneMappingExposure,Hs=e.toonOutlinePass,Js=e.transformDirection,Ks=e.transformNormal,Qs=e.transformNormalToView,$s=e.transformedBentNormalView,el=e.transformedBitangentView,tl=e.transformedBitangentWorld,rl=e.transformedClearcoatNormalView,al=e.transformedNormalView,ol=e.transformedNormalWorld,il=e.transformedTangentView,nl=e.transformedTangentWorld,sl=e.transmission,ll=e.transpose,cl=e.tri,ml=e.tri3,pl=e.triNoise3D,dl=e.triplanarTexture,ul=e.triplanarTextures,gl=e.trunc,hl=e.tslFn,xl=e.uint,fl=e.uniform,bl=e.uniformArray,wl=e.uniformGroup,vl=e.uniforms,Sl=e.userData,Tl=e.uv,_l=e.uvec2,Vl=e.uvec3,yl=e.uvec4,Dl=e.Var,Ml=e.varying,Fl=e.varyingProperty,Cl=e.vec2,Il=e.vec3,Pl=e.vec4,Nl=e.vectorComponents,Rl=e.velocity,Bl=e.vertexColor,Ll=e.vertexIndex,kl=e.vibrance,Al=e.viewZToLogarithmicDepth,Gl=e.viewZToOrthographicDepth,Ol=e.viewZToPerspectiveDepth,Wl=e.viewport,jl=e.viewportBottomLeft,Ul=e.viewportCoordinate,zl=e.viewportDepthTexture,ql=e.viewportLinearDepth,El=e.viewportMipTexture,Zl=e.viewportResolution,Xl=e.viewportSafeUV,Yl=e.viewportSharedTexture,Hl=e.viewportSize,Jl=e.viewportTexture,Kl=e.viewportTopLeft,Ql=e.viewportUV,$l=e.wgsl,ec=e.wgslFn,tc=e.workgroupArray,rc=e.workgroupBarrier,ac=e.workgroupId,oc=e.workingToColorSpace,ic=e.xor;export{t as BRDF_GGX,r as BRDF_Lambert,a as BasicShadowFilter,o as Break,pt as Const,i as Continue,n as DFGApprox,s as D_GGX,l as Discard,c as EPSILON,m as F_Schlick,p as Fn,d as INFINITY,u as If,g as Loop,b as NodeAccess,h as NodeShaderStage,x as NodeType,f as NodeUpdateType,w as PCFShadowFilter,v as PCFSoftShadowFilter,S as PI,T as PI2,_ as Return,V as Schlick_to_F0,y as ScriptableNodeResources,D as ShaderNode,M as TBNViewMatrix,F as VSMShadowFilter,C as V_GGX_SmithCorrelated,Dl as Var,I as abs,P as acesFilmicToneMapping,N as acos,R as add,B as addNodeElement,L as agxToneMapping,k as all,A as alphaT,G as and,O as anisotropy,W as anisotropyB,j as anisotropyT,U as any,z as append,q as array,E as arrayBuffer,Z as asin,X as assign,Y as atan,H as atan2,J as atomicAdd,K as atomicAnd,Q as atomicFunc,ie as atomicLoad,$ as atomicMax,ee as atomicMin,te as atomicOr,re as atomicStore,ae as atomicSub,oe as atomicXor,ne as attenuationColor,se as attenuationDistance,le as attribute,ce as attributeArray,me as backgroundBlurriness,pe as backgroundIntensity,de as backgroundRotation,ue as batch,ge as billboarding,he as bitAnd,xe as bitNot,fe as bitOr,be as bitXor,we as bitangentGeometry,ve as bitangentLocal,Se as bitangentView,Te as bitangentWorld,_e as bitcast,Ve as blendBurn,ye as blendColor,De as blendDodge,Me as blendOverlay,Fe as blendScreen,Ce as blur,Ie as bool,Pe as buffer,Ne as bufferAttribute,Re as bumpMap,Be as burn,Le as bvec2,ke as bvec3,Ae as bvec4,Ge as bypass,Oe as cache,We as call,je as cameraFar,Ue as cameraIndex,ze as cameraNear,qe as cameraNormalMatrix,Ee as cameraPosition,Ze as cameraProjectionMatrix,Xe as cameraProjectionMatrixInverse,Ye as cameraViewMatrix,He as cameraWorldMatrix,Je as cbrt,Ke as cdl,Qe as ceil,$e as checker,et as cineonToneMapping,tt as clamp,rt as clearcoat,at as clearcoatRoughness,ot as code,it as color,nt as colorSpaceToWorking,st as colorToDirection,lt as compute,ct as computeSkinning,mt as cond,dt as context,ut as convert,gt as convertColorSpace,ht as convertToTexture,xt as cos,ft as cross,bt as cubeTexture,wt as dFdx,vt as dFdy,St as dashSize,Tt as debug,_t as defaultBuildStages,Vt as defaultShaderStages,yt as defined,Dt as degrees,Mt as deltaTime,Ft as densityFog,Ct as densityFogFactor,It as depth,Pt as depthPass,Nt as difference,Rt as diffuseColor,Bt as directPointLight,Lt as directionToColor,kt as dispersion,At as distance,Gt as div,Ot as dodge,Wt as dot,jt as drawIndex,Ut as dynamicBufferAttribute,zt as element,qt as emissive,Et as equal,Zt as equals,Xt as equirectUV,Yt as exp,Ht as exp2,Jt as expression,Kt as faceDirection,Qt as faceForward,$t as faceforward,er as float,tr as floor,rr as fog,ar as fract,or as frameGroup,ir as frameId,nr as frontFacing,sr as fwidth,lr as gain,cr as gapSize,mr as getConstNodeType,pr as getCurrentStack,dr as getDirection,ur as getDistanceAttenuation,gr as getGeometryRoughness,hr as getNormalFromDepth,xr as getParallaxCorrectNormal,fr as getRoughness,br as getScreenPosition,wr as getShIrradianceAt,vr as getTextureIndex,Sr as getViewPosition,ma as globalId,Tr as glsl,_r as glslFn,Vr as grayscale,yr as greaterThan,Dr as greaterThanEqual,Mr as hash,Fr as highpModelNormalViewMatrix,Cr as highpModelViewMatrix,Ir as hue,Pr as instance,Nr as instanceIndex,Rr as instancedArray,Br as instancedBufferAttribute,Lr as instancedDynamicBufferAttribute,kr as instancedMesh,Ar as int,Gr as inverseSqrt,Or as inversesqrt,Wr as invocationLocalIndex,jr as invocationSubgroupIndex,Ur as ior,zr as iridescence,qr as iridescenceIOR,Er as iridescenceThickness,Zr as ivec2,Xr as ivec3,Yr as ivec4,Hr as js,Jr as label,Kr as length,Qr as lengthSq,$r as lessThan,ea as lessThanEqual,ta as lightPosition,cn as lightProjectionUV,ra as lightTargetDirection,aa as lightTargetPosition,oa as lightViewPosition,ia as lightingContext,na as lights,sa as linearDepth,la as linearToneMapping,ca as localId,pa as log,da as log2,ua as logarithmicDepthToViewZ,ga as loop,ha as luminance,fa as mat2,ba as mat3,wa as mat4,va as matcapUV,Sa as materialAO,Ta as materialAlphaTest,_a as materialAnisotropy,Va as materialAnisotropyVector,ya as materialAttenuationColor,Da as materialAttenuationDistance,Ma as materialClearcoat,Fa as materialClearcoatNormal,Ca as materialClearcoatRoughness,Ia as materialColor,Pa as materialDispersion,Na as materialEmissive,Ra as materialIOR,Ba as materialIridescence,La as materialIridescenceIOR,ka as materialIridescenceThickness,Aa as materialLightMap,Ga as materialLineDashOffset,Oa as materialLineDashSize,Wa as materialLineGapSize,ja as materialLineScale,Ua as materialLineWidth,za as materialMetalness,qa as materialNormal,Ea as materialOpacity,Za as materialPointSize,Xa as materialReference,Ya as materialReflectivity,Ha as materialRefractionRatio,Ja as materialRotation,Ka as materialRoughness,Qa as materialSheen,$a as materialSheenRoughness,eo as materialShininess,to as materialSpecular,ro as materialSpecularColor,ao as materialSpecularIntensity,oo as materialSpecularStrength,io as materialThickness,no as materialTransmission,so as max,lo as maxMipLevel,xa as mediumpModelViewMatrix,co as metalness,mo as min,po as mix,uo as mixElement,go as mod,ho as modInt,xo as modelDirection,fo as modelNormalMatrix,bo as modelPosition,wo as modelRadius,vo as modelScale,So as modelViewMatrix,To as modelViewPosition,_o as modelViewProjection,Vo as modelWorldMatrix,yo as modelWorldMatrixInverse,Do as morphReference,Mo as mrt,Fo as mul,Co as mx_aastep,Io as mx_cell_noise_float,Po as mx_contrast,No as mx_fractal_noise_float,Ro as mx_fractal_noise_vec2,Bo as mx_fractal_noise_vec3,Lo as mx_fractal_noise_vec4,ko as mx_hsvtorgb,Ao as mx_noise_float,Go as mx_noise_vec3,Oo as mx_noise_vec4,Wo as mx_ramplr,jo as mx_ramptb,Uo as mx_rgbtohsv,zo as mx_safepower,qo as mx_splitlr,Eo as mx_splittb,Zo as mx_srgb_texture_to_lin_rec709,Xo as mx_transform_uv,Yo as mx_worley_noise_float,Ho as mx_worley_noise_vec2,Jo as mx_worley_noise_vec3,Ko as negate,Qo as neutralToneMapping,$o as nodeArray,ei as nodeImmutable,ti as nodeObject,ri as nodeObjects,ai as nodeProxy,oi as normalFlat,ii as normalGeometry,ni as normalLocal,si as normalMap,li as normalView,ci as normalWorld,mi as normalize,pi as not,di as notEqual,ui as numWorkgroups,gi as objectDirection,hi as objectGroup,xi as objectPosition,fi as objectRadius,bi as objectScale,wi as objectViewPosition,vi as objectWorldMatrix,Si as oneMinus,Ti as or,_i as orthographicDepthToViewZ,Vi as oscSawtooth,yi as oscSine,Di as oscSquare,Mi as oscTriangle,Fi as output,Ci as outputStruct,Ii as overlay,Pi as overloadingFn,Ni as parabola,Ri as parallaxDirection,Bi as parallaxUV,Li as parameter,ki as pass,Ai as passTexture,Gi as pcurve,Oi as perspectiveDepthToViewZ,Wi as pmremTexture,Xn as pointShadow,ji as pointUV,Ui as pointWidth,zi as positionGeometry,qi as positionLocal,Ei as positionPrevious,Zi as positionView,Xi as positionViewDirection,Yi as positionWorld,Hi as positionWorldDirection,Ji as posterize,Ki as pow,Qi as pow2,$i as pow3,en as pow4,tn as property,rn as radians,an as rand,on as range,nn as rangeFog,sn as rangeFogFactor,ln as reciprocal,mn as reference,pn as referenceBuffer,dn as reflect,un as reflectVector,gn as reflectView,hn as reflector,xn as refract,fn as refractVector,bn as refractView,wn as reinhardToneMapping,vn as remainder,Sn as remap,Tn as remapClamp,_n as renderGroup,Vn as renderOutput,yn as rendererReference,Dn as rotate,Mn as rotateUV,Fn as roughness,Cn as round,In as rtt,Pn as sRGBTransferEOTF,Nn as sRGBTransferOETF,Rn as sampler,Bn as samplerComparison,Ln as saturate,kn as saturation,An as screen,Gn as screenCoordinate,On as screenSize,Wn as screenUV,jn as scriptable,Un as scriptableValue,zn as select,qn as setCurrentStack,En as shaderStages,Zn as shadow,Yn as shadowPositionWorld,Jn as shapeCircle,Hn as sharedUniformGroup,Kn as sheen,Qn as sheenRoughness,$n as shiftLeft,es as shiftRight,ts as shininess,rs as sign,as as sin,os as sinc,is as skinning,ns as smoothstep,ss as smoothstepElement,ls as specularColor,cs as specularF90,ms as spherizeUV,ps as split,ds as spritesheetUV,us as sqrt,gs as stack,hs as step,xs as storage,fs as storageBarrier,bs as storageObject,ws as storageTexture,vs as string,Ss as struct,Ts as sub,_s as subgroupIndex,Vs as subgroupSize,ys as tan,Ds as tangentGeometry,Ms as tangentLocal,Fs as tangentView,Cs as tangentWorld,Is as temp,Ps as texture,Ns as texture3D,Rs as textureBarrier,Bs as textureBicubic,Ls as textureCubeUV,ks as textureLoad,As as textureSize,Gs as textureStore,Os as thickness,Ws as threshold,js as time,Us as timerDelta,zs as timerGlobal,qs as timerLocal,Es as toOutputColorSpace,Zs as toWorkingColorSpace,Xs as toneMapping,Ys as toneMappingExposure,Hs as toonOutlinePass,Js as transformDirection,Ks as transformNormal,Qs as transformNormalToView,$s as transformedBentNormalView,el as transformedBitangentView,tl as transformedBitangentWorld,rl as transformedClearcoatNormalView,al as transformedNormalView,ol as transformedNormalWorld,il as transformedTangentView,nl as transformedTangentWorld,sl as transmission,ll as transpose,cl as tri,ml as tri3,pl as triNoise3D,dl as triplanarTexture,ul as triplanarTextures,gl as trunc,hl as tslFn,xl as uint,fl as uniform,bl as uniformArray,wl as uniformGroup,vl as uniforms,Sl as userData,Tl as uv,_l as uvec2,Vl as uvec3,yl as uvec4,Ml as varying,Fl as varyingProperty,Cl as vec2,Il as vec3,Pl as vec4,Nl as vectorComponents,Rl as velocity,Bl as vertexColor,Ll as vertexIndex,kl as vibrance,Al as viewZToLogarithmicDepth,Gl as viewZToOrthographicDepth,Ol as viewZToPerspectiveDepth,Wl as viewport,jl as viewportBottomLeft,Ul as viewportCoordinate,zl as viewportDepthTexture,ql as viewportLinearDepth,El as viewportMipTexture,Zl as viewportResolution,Xl as viewportSafeUV,Yl as viewportSharedTexture,Hl as viewportSize,Jl as viewportTexture,Kl as viewportTopLeft,Ql as viewportUV,$l as wgsl,ec as wgslFn,tc as workgroupArray,rc as workgroupBarrier,ac as workgroupId,oc as workingToColorSpace,ic as xor};
