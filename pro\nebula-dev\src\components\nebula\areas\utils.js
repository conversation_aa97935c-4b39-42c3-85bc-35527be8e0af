import { CLICK_FLAG } from "./constant";
import { Raycaster, Vector2, Vector3 } from "three";
/**
 * 计算单层邻域坐标位置
 * @param {object} option 参数
 */
export function computeAreasPos(
  option = {
    count: 10, //邻域数量
    z: 0, // 层级y值
    baseRadius: 5, // 基础半径
    radiusStep: 2, // 半径步长
    angleOffsetStep: Math.PI / 10, // 每圈错开18度
  }
) {
  const { count, z, baseRadius, radiusStep, angleOffsetStep } = option;
  const posArr = [];
  let y = z;
  // for (let i = 0; i < count; i++) {
  //   const r = baseRadius + i * radiusStep; // 当前层半径
  //   const angleOffset = i * angleOffsetStep; // 当前层旋转角度

  //   for (let j = 0; j < 5; j++) {
  //     // 五角星跳跃144度
  //     const angle = angleOffset + j * ((2 * Math.PI * 2) / 5); // = j * 144°
  //     const x = r * Math.cos(angle);
  //     const y = r * Math.sin(angle);
  //     posArr.push([x, z, y]);
  //   }
  // }

  const interval = 3;
  let index = 0;
  for (let round = 1, s = count; s != 0; ++round) {
    let num = interval * round;
    if (num < s) {
      s -= num;
    } else {
      num = s;
      s = 0;
    }
    //重新计算y值
    y += Math.sin(round) * 0.5;
    const r = round * 3;
    for (let i = 0; i < num; ++i) {
      const x = r * Math.cos((2 * Math.PI * i) / num);
      const z = r * Math.sin((2 * Math.PI * i) / num);
      if (index < count) {
        posArr.push([x, y, z]);
      }
    }
  }

  return posArr;
}

let index = 1;
/**
 *给星云绑定允许点击标记
 * @param {THREE.Group} areaGroup 星云组
 */
export function bindClick(areaGroup) {
  //允许自定userDta
  areaGroup.userData[CLICK_FLAG] = true;
  return areaGroup;
}

/**
 * 点击事件
 */
export function createClickEvent(options) {
  const { ele, scene, clickCallback, camera } = options;
  const { width, height } = ele.getBoundingClientRect();
  let raycaster = new Raycaster();

  //清空闭包造成的内存泄漏
  function clear() {
    width = null;
    height = null;
    raycaster = null;
  }
  // 更新尺寸
  function updateSize(w, h) {
    width = w;
    height = h;
  }
  //生成的event事件
  function event(e) {
    const { offsetX, clientX, offsetY, clientY, button } = e;
    raycaster.setFromCamera(
      new Vector2((offsetX / width) * 2 - 1, -(offsetY / height) * 2 + 1),
      camera
    );
    const intersects = raycaster.intersectObject(scene, true); // 递归检测所有子对象

    if (intersects.length > 0) {
      // 查找第一个具有点击标记的对象
      for (let i = 0; i < intersects.length; i++) {
        const intersect = intersects[i];
        let currentObject = intersect.object;

        // 向上遍历对象层次结构，查找具有点击标记的对象
        while (currentObject) {
          if (currentObject.userData && currentObject.userData[CLICK_FLAG]) {
            // 找到了具有点击标记的对象，触发回调
            clickCallback(currentObject);
            return; // 找到第一个有效对象后立即返回
          }
          currentObject = currentObject.parent;
        }
      }
    }
  }
  return {
    clear,
    event,
    updateSize,
  };
}

/**
 * 通过相机位置和目标星云定位子层星云集合位置
 */
export function computeSubAreasPos(cameraPos, targetAreaPos, distance) {
  const direction = new Vector3()
    .subVectors(targetAreaPos, cameraPos)
    .normalize();
  return cameraPos.clone().add(direction.multiplyScalar(distance));
}
