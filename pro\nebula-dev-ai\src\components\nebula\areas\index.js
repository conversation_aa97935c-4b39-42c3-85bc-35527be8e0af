import initCloud from "./cloud";
import generateStars from "./stars";
import generateCenterLight from "./center";
import { computeAreasPos } from "./utils";
import * as THREE from "three";
import { createClickEvent, bindClick } from "./utils";
import { Tween, Easing } from "three/examples/jsm/libs/tween.module.js";

// 全局变量存储动画状态
let isAnimating = false;
let currentLevel = 0;
let nebulaLevels = []; // 存储不同层级的星云

/**
 *创建一个邻域集合
 */
export default async function generateArea(options) {
  const { ele, camera, scene } = options;
  const generateCloud = await initCloud();
  const originArea = createArea(generateCloud);

  const areasGroup = new THREE.Group();
  const count = 10;
  const posArr = computeAreasPos({
    count, //邻域数量
    z: 0, // 层级y值
    baseRadius: 5, // 基础半径
    radiusStep: 2, // 半径步长
    angleOffsetStep: Math.PI / 10, // 每圈错开18度
  });

  // 为每个星云添加层级信息
  posArr.forEach((pos, index) => {
    const area = originArea.clone();
    area.position.set(pos[0], pos[1], pos[2]);
    area.userData.level = 0; // 第一层级
    area.userData.index = index;
    area.userData.hasSubNebulas = true; // 标记有子星云
    areasGroup.add(area);
  });

  // 初始化星云层级数组
  nebulaLevels[0] = areasGroup;

  //创建点击事件
  const { event } = createClickEvent({
    ele,
    scene: scene, // 改为整个场景，这样可以点击所有层级的星云
    camera,
    clickCallback: (nebulaG) => {
      if (isAnimating) return; // 防止动画期间重复点击

      //获取世界坐标
      const worldPos = nebulaG.position.clone();
      worldPos.applyMatrix4(nebulaG.matrixWorld);

      // 进入子星云动画
      enterSubNebula(nebulaG, worldPos, camera, scene, generateCloud);
    },
  });
  ele.addEventListener("click", event);

  return areasGroup;
}

/**
 * 进入子星云的动画效果
 */
async function enterSubNebula(
  clickedNebula,
  worldPos,
  camera,
  scene,
  generateCloud
) {
  isAnimating = true;

  // 1. 计算摄像机目标位置（在点击星云和摄像机的连线上）
  const cameraPos = camera.position.clone();
  const direction = worldPos.clone().sub(cameraPos).normalize();
  const targetCameraPos = worldPos.clone().add(direction.multiplyScalar(3)); // 距离星云3个单位

  // 2. 创建子星云组
  const subNebulaGroup = await createSubNebulaGroup(generateCloud, worldPos);
  scene.add(subNebulaGroup);

  // 初始时子星云不可见
  // subNebulaGroup.visible = false;

  // 3. 开始动画序列
  return new Promise((resolve) => {
    // 第一阶段：摄像机移动到目标位置，同时当前星云淡出
    const phase1Duration = 1500;

    new Tween({
      cameraX: cameraPos.x,
      cameraY: cameraPos.y,
      cameraZ: cameraPos.z,
      nebulaOpacity: 1.0,
      nebulaScale: 1.0,
    })
      .to(
        {
          cameraX: targetCameraPos.x,
          cameraY: targetCameraPos.y,
          cameraZ: targetCameraPos.z,
          nebulaOpacity: 0.0,
          nebulaScale: 0.1,
        },
        phase1Duration
      )
      .easing(Easing.Quadratic.InOut)
      .onUpdate((values) => {
        // 更新摄像机位置
        camera.position.set(values.cameraX, values.cameraY, values.cameraZ);
        camera.lookAt(worldPos);

        // 淡出当前星云
        updateNebulaOpacity(clickedNebula, values.nebulaOpacity);
        updateNebulaScale(clickedNebula, values.nebulaScale);
      })
      .onComplete(() => {
        // 隐藏当前星云
        // clickedNebula.visible = false;

        // 第二阶段：子星云淡入
        startSubNebulaFadeIn(subNebulaGroup, resolve);
      })
      .start();
  });
}

/**
 * 子星云淡入动画
 */
function startSubNebulaFadeIn(subNebulaGroup, resolve) {
  const phase2Duration = 1000;

  // 显示子星云组
  subNebulaGroup.visible = true;

  new Tween({
    opacity: 0.0,
    scale: 0.1,
  })
    .to(
      {
        opacity: 1.0,
        scale: 1.0,
      },
      phase2Duration
    )
    .easing(Easing.Quadratic.Out)
    .onUpdate((values) => {
      // 更新所有子星云的透明度和缩放
      subNebulaGroup.children.forEach((child) => {
        updateNebulaOpacity(child, values.opacity);
        updateNebulaScale(child, values.scale);
      });
    })
    .onComplete(() => {
      isAnimating = false;
      currentLevel++;
      resolve();
    })
    .start();
}

/**
 * 创建子星云组
 */
async function createSubNebulaGroup(generateCloud, centerPos) {
  const subGroup = new THREE.Group();
  const subCount = 6; // 子星云数量

  // 在点击星云周围创建子星云
  const subPosArr = computeAreasPos({
    count: subCount,
    z: centerPos.z,
    baseRadius: 2, // 更小的半径
    radiusStep: 1,
    angleOffsetStep: Math.PI / 8,
  });

  for (let i = 0; i < subPosArr.length; i++) {
    const pos = subPosArr[i];
    const subArea = createArea(generateCloud);

    // 相对于中心位置偏移
    subArea.position.set(
      centerPos.x + pos[0],
      centerPos.y + pos[1],
      centerPos.z + pos[2]
    );

    subArea.userData.level = currentLevel + 1;
    subArea.userData.index = i;
    subArea.userData.hasSubNebulas = true;

    subGroup.add(subArea);
  }

  return subGroup;
}

/**
 * 更新星云透明度
 */
function updateNebulaOpacity(nebula, opacity) {
  nebula.traverse((child) => {
    if (child.material) {
      if (Array.isArray(child.material)) {
        child.material.forEach((mat) => {
          mat.opacity = opacity;
          mat.transparent = true;
        });
      } else {
        child.material.opacity = opacity;
        child.material.transparent = true;
      }
    }
  });
}

/**
 * 更新星云缩放
 */
function updateNebulaScale(nebula, scale) {
  nebula.scale.set(scale, scale, scale);
}

/**
 * 创建一个邻域
 */
export function createArea(generateCloud) {
  const area = new THREE.Group();
  const cloud = generateCloud(); //nebula
  const { stars } = generateStars(); //star
  const centerLights = generateCenterLight(); //center light
  // const margGroup = new THREE.Group();
  // margGroup.add(cloud, centerLights);

  // area.add(bindClick(LDrawUtils.mergeObject(margGroup)));
  area.add(bindClick(cloud), centerLights, stars);
  // area.add(stars);

  return area;
}
