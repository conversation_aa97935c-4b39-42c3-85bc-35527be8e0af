# 星云点击进入子星云演示说明

## 功能演示

这个项目实现了一个交互式的3D星云系统，用户可以点击星云来"进入"其内部，查看子级星云。

## 如何运行

1. 确保已安装依赖：
   ```bash
   npm install
   ```

2. 启动开发服务器：
   ```bash
   npm run dev
   ```

3. 在浏览器中打开显示的URL（通常是 http://localhost:5173）

## 交互说明

### 基本操作
- **点击星云**：点击任意星云可以进入其子星云系统
- **观察动画**：注意摄像机的平滑移动和星云的淡入淡出效果

### 动画效果详解

1. **第一阶段（1.5秒）**：
   - 摄像机从当前位置移动到被点击星云的位置
   - 被点击的星云逐渐变透明并缩小
   - 摄像机始终朝向星云中心

2. **第二阶段（1秒）**：
   - 当前星云完全消失
   - 6个子星云在周围逐渐显现
   - 子星云从小到大缩放并变为不透明

### 技术特点

- **位置对齐**：摄像机、原星云和子星云始终在一条直线上
- **防重复点击**：动画期间禁用点击事件，避免冲突
- **层级支持**：子星云也可以继续点击，支持多层级探索
- **平滑动画**：使用Three.js内置的TWEEN库实现流畅动画

### 预期效果

用户点击星云后应该看到：
1. 摄像机平滑地"飞向"被点击的星云
2. 原星云逐渐消失
3. 新的子星云群组在同一位置出现
4. 整个过程大约2.5秒，动画流畅自然

### 故障排除

如果动画不工作，请检查：
1. 浏览器控制台是否有错误信息
2. 星云模型是否正确加载（需要 `/xy2.glb` 文件）
3. 点击是否正确命中星云对象

### 自定义参数

可以在 `src/components/nebula/areas/index.js` 中调整：
- 动画持续时间
- 子星云数量和分布
- 摄像机移动距离
- 缓动函数类型

## 代码结构

- `src/components/nebula/areas/index.js` - 主要动画逻辑
- `src/components/nebula/index.vue` - 组件入口和动画循环
- `src/components/nebula/areas/utils.js` - 点击事件处理
- `src/components/nebula/areas/cloud.js` - 星云模型加载
