import { Tween, Easing } from "three/examples/jsm/libs/tween.module.js";

export function enterSubNebulaAni(options) {
  const { camera, target, nextAreaPos, controls, crtG, nextG, preG } = options;
  nextG.scale.set(0, 0, 0);
  return new Promise((resolve) => {
    new Tween({
      x: camera.position.x,
      y: camera.position.y,
      z: camera.position.z,
      crtGOpacity: 1,
      tx: controls.target.x,
      ty: controls.target.y,
      tz: controls.target.z,
    })
      .to(
        {
          x: target.x,
          y: target.y,
          z: target.z,
          crtGOpacity: 0,
          tx: nextAreaPos.x,
          ty: nextAreaPos.y,
          tz: nextAreaPos.z,
        },
        1500
      )
      .onUpdate(({ x, y, z, crtGOpacity, tx, ty, tz }) => {
        camera.position.set(x, y, z);
        crtG.traverse((child) => {
          if (child.material) {
            child.material.opacity = crtGOpacity;
          }
        });
        controls.target.set(tx, ty, tz);
        nextG.traverse((child) => {
          if (child.material) {
            child.material.opacity = 1 - crtGOpacity;
          }
        });
        // 缩放
        if (crtGOpacity.toFixed(1) == 0.5) {
          scaleSubNebulaAni({
            nextG,
            startScalar: 0,
            endScalar: 1,
          });
        }
        camera.lookAt(controls.target);
      })
      .easing(Easing.Quadratic.InOut)
      .onComplete(() => {
        resolve();
      })
      .onStart()
      .start();
  });
}

export function scaleSubNebulaAni(options) {
  const { nextG, startScalar, endScalar, duration } = options;
  return new Promise((resolve) => {
    new Tween({
      s: startScalar,
    })
      .to(
        {
          s: endScalar,
        },
        duration ?? 900
      )
      .onUpdate(({ s }) => {
        nextG.scale.set(s, s, s);
      })
      .easing(Easing.Quadratic.InOut)
      .onComplete(() => {
        resolve();
      })
      .onStart()
      .start();
  });
}
