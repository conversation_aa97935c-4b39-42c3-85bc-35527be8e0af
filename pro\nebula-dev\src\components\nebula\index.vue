<template>
  <div class="nebula-container">
    <div class="btn-container">
      <button @click="backToPreAreas">Back</button>
    </div>
    <div ref="threeContainer"></div>
  </div>
</template>

<script setup>
import {
  Tween,
  Easing,
  update as tweenUpdate,
} from "three/examples/jsm/libs/tween.module.js";
import { onMounted, ref } from "vue";
import useThree from "../hooks/useThree";

import useCloud from "./cloud";
import useStars from "./stars";
import useCenter from "./center";
import useTest from "./area";
import initArea from "./areas";

const threeContainer = ref(null);
const { scene, renderer, camera, controls } = useThree();
// const { cloudMaterial } = useCloud(scene);
// useTest(scene);
// const { update } = useStars(scene);
// useCenter(scene);

function animate() {
  renderer.render(scene, camera);
  tweenUpdate();
  requestAnimationFrame(animate);
}

async function init() {
  threeContainer.value.appendChild(renderer.domElement);
  initArea({
    ele: threeContainer.value,
    camera,
    scene,
    controls,
  });

  animate();
}
onMounted(init);
</script>

<style scoped>
.nebula-container {
  position: relative;
}
.btn-container{
  
}
</style>
