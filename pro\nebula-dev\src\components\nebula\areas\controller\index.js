import { createClickEvent, bindClick } from "../utils";
import { computeAreasPos, computeSubAreasPos } from "../utils";
import { CLICK_FLAG } from "../constant";

import { enterSubNebulaAni } from "../animation";
import * as THREE from "three";

// const matrial = new THREE.MeshBasicMaterial({
//   color: 0x00ff00,
//   // 线框
//   wireframe: true,
// });
// const geometry = new THREE.BoxGeometry(1, 1, 1);
// const cube = new THREE.Mesh(geometry, matrial);
// const cube2 = cube.clone();
// cube.material.color.set(0xff0000);
// cube.position.set(0, 3, 0);

export default class AreaController {
  //所有星云数据模型
  data = null;
  //当前星云集合
  crtG = new THREE.Group();
  //pre
  preG = new THREE.Group();
  //next
  next = new THREE.Group();
  //场景
  scene = null;
  //被clone的星云模型
  cloneModel = null;
  //相机
  camera = null;
  //控制器
  controls = null;
  constructor(options) {
    const { data, currentId, scene, cloneModel, ele, camera, controls } =
      options;
    this.data = new Map(data.map((item) => [item.areaId, item]));
    this.camera = camera;
    this.cloneModel = cloneModel;
    this.controls = controls;

    this.crtG.add(...this.createAreaModel(currentId));
    scene.add(this.crtG);
    scene.add((this.preG = this.crtG));
    scene.add(this.next);
    this.scene = scene;
    this.initClickEvent(ele, camera, scene);
    // scene.add(cube);
    // scene.add(cube2);
  }

  /**
   * 根据数据创星云Model
   */
  createAreaModel(currentId) {
    const data = this.data.get(currentId).children;
    return computeAreasPos({
      count: data.length, //邻域数量
      z: 0, // 层级y值
      baseRadius: 5, // 基础半径
      radiusStep: 2, // 半径步长
      angleOffsetStep: Math.PI / 10, // 每圈错开18度
    }).map((pos, index) => {
      const area = this.cloneModel.clone();
      area.traverse((child) => {
        if (child.material) {
          child.material = child.material.clone();
        }
      });
      const areaInfo = this.data.get(data[index]);
      area.position.set(...pos);
      area.setAreaId(areaInfo.areaId);
      return area;
    });
  }

  /**
   * 初始化点击事件
   */
  initClickEvent(ele, camera, areaGroup) {
    const { clear, event, updateSize } = createClickEvent({
      ele,
      scene: areaGroup,
      camera,
      clickCallback: (nebulaG) => {
        //获取世界坐标
        const worldPos = nebulaG.position.clone();
        worldPos.applyMatrix4(nebulaG.matrixWorld);
        this.enterNextAreas(nebulaG.userData.areaId, worldPos);
        // cube.position.copy(worldPos);
      },
    });
    ele.addEventListener("pointerdown", event);
  }

  /**
   * 进入子邻域
   */
  enterNextAreas(areaId, crtAreaPos) {
    console.log(areaId, this.data.get(areaId));
    if (!this.data.get(areaId).children.length) return;
    // this.next.remove(...this.next.children);

    this.next.add(...this.createAreaModel(areaId));
    this.next.traverse((child) => {
      if (child.material) {
        child.material.opacity = 0;
      }
    });
    const nextGPos = computeSubAreasPos(this.camera.position, crtAreaPos, 18);
    this.next.position.set(...nextGPos.toArray());
    enterSubNebulaAni({
      camera: this.camera,
      target: crtAreaPos,
      crtG: this.crtG,
      preG: this.preG,
      nextG: this.next,
      nextAreaPos: nextGPos,
      controls: this.controls,
    }).then(() => {
      this.pre = this.crtG;
      this.crtG = this.next;
      this.next = new THREE.Group();
      this.scene.add(this.next);
    });
  }

  /**
   * 退回邻域
   */
  backToPreAreas() {
    console.log("back");
  }
}
