# 星云点击进入子星云动画效果

## 功能描述

实现了点击星云后进入其子星云的动画效果，包含以下特性：

### 动画流程

1. **摄像机移动**：从当前位置平滑移动到点击星云的位置
2. **星云淡出**：被点击的星云逐渐变透明并缩小
3. **子星云淡入**：新的子星云组逐渐显现并放大

### 技术实现

#### 核心文件修改

- `src/components/nebula/areas/index.js` - 主要实现文件
- `src/components/nebula/index.vue` - 集成TWEEN动画更新循环

#### 关键函数

1. **`enterSubNebula()`** - 主动画控制函数
   - 计算摄像机目标位置
   - 创建子星云组
   - 执行两阶段动画

2. **`createSubNebulaGroup()`** - 创建子星云组
   - 在点击星云周围生成6个子星云
   - 使用更小的半径和间距

3. **`updateNebulaOpacity()`** - 更新星云透明度
   - 遍历星云的所有子对象
   - 设置材质透明度

4. **`updateNebulaScale()`** - 更新星云缩放
   - 统一缩放星云的x、y、z轴

#### 动画参数

- **第一阶段持续时间**：1500ms（摄像机移动 + 星云淡出）
- **第二阶段持续时间**：1000ms（子星云淡入）
- **缓动函数**：
  - 第一阶段：`Easing.Quadratic.InOut`
  - 第二阶段：`Easing.Quadratic.Out`

#### 位置计算

摄像机目标位置计算：
```javascript
const direction = worldPos.clone().sub(cameraPos).normalize();
const targetCameraPos = worldPos.clone().add(direction.multiplyScalar(3));
```

确保摄像机、点击的星云和子星云在一条直线上。

### 使用方法

1. 启动项目：`npm run dev`
2. 点击任意星云
3. 观察动画效果：
   - 摄像机移动到星云位置
   - 当前星云消失
   - 子星云出现

### 特性

- **防重复点击**：动画期间禁用点击事件
- **层级管理**：支持多层级星云结构
- **平滑动画**：使用TWEEN.js实现流畅的动画效果
- **响应式点击**：子星云也可以继续点击进入下一层

### 配置参数

可以通过修改以下参数来调整效果：

```javascript
// 子星云数量
const subCount = 6;

// 子星云半径
baseRadius: 2,
radiusStep: 1,

// 摄像机距离
direction.multiplyScalar(3)

// 动画时长
const phase1Duration = 1500;
const phase2Duration = 1000;
```

### 依赖

- Three.js
- Three.js内置的TWEEN模块
- Vue 3

### 注意事项

1. 确保星云模型有正确的材质设置以支持透明度变化
2. 动画期间会禁用用户交互以防止冲突
3. 子星云会继承父星云的点击功能
