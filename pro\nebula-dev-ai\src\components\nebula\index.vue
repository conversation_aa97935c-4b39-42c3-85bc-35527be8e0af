<template>
  <div ref="threeContainer"></div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import useThree from "../hooks/useThree";
import generateArea from "./areas";
import { update as tweenUpdate } from "three/examples/jsm/libs/tween.module.js";

const threeContainer = ref(null);
const { scene, renderer, camera } = useThree();

function animate() {
  // 更新TWEEN动画
  tweenUpdate();

  renderer.render(scene, camera);
  requestAnimationFrame(animate);
}

async function init() {
  threeContainer.value.appendChild(renderer.domElement);
  const area = await generateArea({
    ele: threeContainer.value,
    camera,
    scene,
  });
  scene.add(area);

  animate();
}
onMounted(init);
</script>

<style scoped></style>
