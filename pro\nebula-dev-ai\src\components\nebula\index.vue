<template>
  <div ref="threeContainer"></div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import useThree from "../hooks/useThree";
import generateArea from "./areas";

const threeContainer = ref(null);
const { scene, renderer, camera } = useThree();

function animate() {
  renderer.render(scene, camera);
  requestAnimationFrame(animate);
}

async function init() {
  threeContainer.value.appendChild(renderer.domElement);
  const area = await generateArea({
    ele: threeContainer.value,
    camera,
    scene,
  });
  scene.add(area);

  animate();
}
onMounted(init);
</script>

<style scoped></style>
