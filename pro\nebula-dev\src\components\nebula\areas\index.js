import initCloud from "./cloud";
import generateStars from "./stars";
import generateCenterLight from "./center";
import { computeAreasPos } from "./utils";
import * as THREE from "three";
import { createClickEvent, bindClick } from "./utils";
import mockData from "../data/mock.json";
import AreaController from "./controller";

/**
 *创建一个邻域集合
 */
export default async function initArea(options) {
  const { ele, camera, scene, controls } = options;
  const generateCloud = await initCloud();
  const originArea = createArea(generateCloud);

  new AreaController({
    scene,
    ele,
    camera,
    controls,
    data: mockData,
    currentId: "0",
    cloneModel: originArea,
  });
}

/**
 * 创建一个邻域
 */
export function createArea(generateCloud) {
  THREE.Group.prototype.setAreaId = function (id) {
    if (this.children.length) this.children[0].userData.areaId = id;
  };

  const area = new THREE.Group();
  const cloud = generateCloud(); //nebula
  const { stars, update } = generateStars(); //star
  const centerLights = generateCenterLight(); //center light
  area.add(bindClick(cloud), centerLights, stars);

  return area;
}
